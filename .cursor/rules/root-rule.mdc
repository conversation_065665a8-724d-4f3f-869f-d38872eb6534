---
description: Core architecture & coding standards for WorkflowMapperAgent monorepo (root rule)
globs:
alwaysApply: true
---
# WorkflowMapperAgent – Cursor Rules

## Version

_v1.2 – Consolidated with docs/tech-specs/00_structure.mdx as single source of truth._

## Purpose

Provide Cursor's Agent and Cmd‑K AI with persistent, project‑wide guidance for code generation, refactors, and documentation tasks. **This rule is always applied.**

**📋 For complete project structure, conventions, and governance:** See [`docs/tech-specs/00_structure.mdx`](../../docs/tech-specs/00_structure.mdx)

---

## 0. Repo Philosophy

- **Graph‑first backend** – The JSON‑LD graph is the canonical state; every API mutates or queries it—no parallel domain models.
- **Graph schema versioning & observability** – All changes to the graph schema must be versioned and observable for stability and traceability.
- **Dual-mode scan** – The CLI supports both full and incremental scans; contributors should understand why two CLI targets exist (full for cold start, incremental for fast dev/test cycles).
- **Type‑safe monorepo** – Two workspaces (`apps/api`, `apps/web`) in strict TypeScript, sharing types in `packages/shared`.
- **Incremental & observable** – Prefer additive PRs and surface coverage / audit metrics; avoid mass renames.
- **Human‑review friendly** – Emit TODOs or request clarification instead of guessing.

---

## 1. Directory Structure

**📁 See [`docs/tech-specs/00_structure.mdx`](../../docs/tech-specs/00_structure.mdx) for complete structure and conventions.**

**Quick Reference:**
```
/
├─ apps/
│  ├─ api/                # Express + ts‑node backend
│  └─ web/                # React 18 + Vite SPA
├─ packages/
│  ├─ shared/             # common TypeScript types & utils
│  └─ (future libs)/
├─ docs/tech-specs/       # milestone & domain specs
└─ .cursor/rules/         # Cursor-specific guidance
```

> **Cursor behaviour:** When generating code, place files in their correct sub‑folder and update relevant imports/exports. Generate matching unit tests alongside new modules.

---

## 2. Tech Stack & Coding Standards

**🔧 See [`docs/tech-specs/00_structure.mdx`](../../docs/tech-specs/00_structure.mdx) for complete build tools, testing layers, and coding conventions.**

**Key Cursor-specific patterns:**
- Use strict TypeScript (`noImplicitAny`, `noUncheckedIndexedAccess`)
- Named exports only; **forbid non-deterministic re-exports** (`export *`)
- Functions ≤60 lines; split otherwise
- All route handlers return `Result<T, E>` objects from `packages/shared/Result.ts`
- Express routes in `apps/api/src/routes.ts`; controllers in `apps/api/src/controllers/*`
- React components in `apps/web/src/components` with colocated CSS Modules
- Re‑export cross‑cutting interfaces from `packages/shared/index.ts`

---

## 3. Milestones & Branching

**📋 See [`docs/tech-specs/00_structure.mdx`](../../docs/tech-specs/00_structure.mdx) for complete branching strategy and release flow.**

**Quick Reference - Milestones:**
| ID | Summary                  | Success metric                                 |
|----|--------------------------|------------------------------------------------|
| M0 | Monorepo skeleton & CI   | `pnpm test` passes < 60 s; CI pipeline green on main; Docker image builds |
| M1 | Static graph builder     | CLI `pnpm run build-graph` outputs JSON‑LD graph |
| M2 | Incremental diff mode    | Graph update < 1 s for 3‑file change           |
| M3 | Spec generator API       | `GET /api/specs` returns valid OpenAPI YAML    |
| M4 | Docs dashboard UI        | Frontend `/docs` shows graph + docs            |
| M5 | Code translation service | `POST /api/translate` returns compilable Go    |

**Cursor branching behavior:**
- Create branches as `m{n}-{short-task}` (e.g. `m1-parser`)
- Run `pnpm test --recursive` and `eslint` before each commit

---

## 4. Cursor‑specific Guidance

- **Always applied:** This rule is always applied; reference via @Cursor Rules if context is lost
- **Precise context:** Use `@code`, `@file`, `@folder` symbols to provide precise context
- **No hallucination:** If any rule here blocks generation, Cursor must ask for clarification rather than hallucinate
- **Documentation first:** Always check [`docs/tech-specs/00_structure.mdx`](../../docs/tech-specs/00_structure.mdx) for current project conventions
- **Version updates:** When refactoring, update rule version header at top so Cursor re-indexes

## Quick Checklist

Before generating code, ensure:
- [ ] Target directory follows `apps/` or `packages/` structure
- [ ] Using named exports only (no `export *`)
- [ ] API routes return `Result<T, E>` type
- [ ] Tests are colocated with source files
- [ ] TypeScript strict mode compliance

If any answer is no, request clarification.

---

_End of rule file_