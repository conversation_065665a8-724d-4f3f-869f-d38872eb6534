---
description:
globs:
alwaysApply: false
---
description: Core architecture & coding standards for WorkflowMapperAgent monorepo (root rule)
alwaysApply: true

WorkflowMapperAgent – Project Rules

Purpose  Provide Cursor’s Agent and Cmd‑K AI with persistent, project‑wide guidance so all code generation, refactors, and documentation tasks align with the architecture and milestones of the WorkflowMapperAgent.This rule is always applied.

0. Repo Philosophy

Graph‑first backend – All business logic flows through the JSON‑LD workflow graph built by the agent.

Type‑safe monorepo – Two workspaces (backend/, frontend/) in strict TypeScript, sharing types in /shared.

Incremental & observable – Prefer additive PRs and surface coverage / audit metrics; avoid mass renames.

Human‑review friendly – Emit TODOs or request clarification instead of guessing.

1. Directory Structure

/
├─ backend/               # Express + ts‑node backend
│  ├─ src/
│  │   ├─ agent/          # graph builder logic & tools
│  │   ├─ api/            # REST controllers & routes
│  │   └─ index.ts        # app bootstrap
│  ├─ tests/              # jest unit & integration tests
│  └─ tsconfig.json
│
├─ frontend/              # React 18 + Vite SPA
│  ├─ src/
│  │   ├─ components/
│  │   ├─ pages/
│  │   └─ hooks/
│  ├─ tests/              # vitest + RTL tests
│  └─ tsconfig.json
│
├─ shared/                # common TypeScript types & utils
├─ docs/                  # generated markdown docs (git‑ignored)
├─ .cursor/
│   └─ rules/             # additional scoped rules if needed
├─ package.json           # root (pnpm workspace)
└─ README.md

Cursor behaviour: When generating code, place files in their correct sub‑folder and update relevant imports / exports. Generate matching unit tests alongside new modules.

2. Tech Stack & Dependencies

Layer

Runtime / Core libs

Validation / State

Test libs

Backend

Node 20, Express ^5, ts‑node-dev

Zod

Jest, Supertest

Frontend

React 18, Vite, React Router v6, Zustand

Zod (client)

Vitest, React Testing Library

Shared

TypeScript 5.4, eslint + prettier, tsc‑aliases

—

—

Add new deps by editing package.json and documenting them in /docs/dependencies.md.

3. Coding Standards

General

Use strict TypeScript (noImplicitAny, noUncheckedIndexedAccess).

Named exports only; no default exports.

Functions ≤60 lines; split otherwise.

Lint with eslint --max-warnings 0; format with Prettier.

Backend

Express routes live in backend/src/api/routes.ts; controllers in backend/src/api/controllers/*.

Validation via Zod schemas colocated with DTOs.

All route handlers return Result<T, E> objects (

type Result<T,E extends Error> = { ok: true; data: T } | { ok: false; error: E };

).

Centralised error middleware at backend/src/api/errorHandler.ts.

Frontend

Functional React components only; prefer hooks.

Components live under frontend/src/components with .tsx extension and colocated CSS Modules.

Use React Query for async data; Zustand for client state.

Shared

Re‑export cross‑cutting interfaces from shared/index.ts to avoid deep imports.

4. Milestones & Branching

ID

Summary

Success metric

M0

Monorepo skeleton & CI

pnpm test passes < 60 s

M1

Static graph builder

CLI pnpm run build-graph outputs JSON‑LD graph

M2

Incremental diff mode

Graph update < 1 s for 3‑file change

M3

Spec generator API

GET /api/specs returns valid OpenAPI YAML

M4

Docs dashboard UI

Frontend /docs shows graph + docs

M5

Code translation service

POST /api/translate returns compilable Go

Create branches as m{n}-{short-task} (e.g. m1-parser). Run pnpm test --recursive and eslint before each commit.

5. Cursor‑specific Guidance

This rule is always applied; reference via @Cursor Rules if context is lost.

Use @code, @file, @folder symbols to provide precise context.

If any rule here blocks generation, Cursor must ask for clarification rather than hallucinate.

Quick Checklist



If any answer is no, request clarification.

End of rule file

