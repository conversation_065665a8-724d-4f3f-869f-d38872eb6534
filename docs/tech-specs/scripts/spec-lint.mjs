#!/usr/bin/env node
/* minimal MDX linter: checks required headings & front-matter keys */

import fs from "fs";
import path from "path";
import matter from "gray-matter";

const requiredFront = [
  "title",
  "description",
  "created",
  "version",
  "status",
  "tags"
];
const requiredHeads = [
  "## 🧳 Toolchain Versions",
  "## 🎯 Definition of Done",
  "## 📦 Deliverables",
  "## 🗂 Directory", // handles variant later
  "## 🗂 Directory / API Diagram",
  "## 🧠 Key Decisions",
  "## ✅ Success Criteria",
  "## 🔨 Task Breakdown",
  "## 🤖 CI Pipeline",
  "## 🧪 Acceptance Tests"
];

if (process.argv.length < 3) {
  console.error("Usage: node spec-lint.mjs <spec-file>");
  process.exit(1);
}

const file = fs.readFileSync(path.resolve(process.argv[2]), "utf8");
const { content, data } = matter(file);

/* Front-matter */
const missingFront = requiredFront.filter((k) => !(k in data));
if (missingFront.length) {
  console.error("Missing front-matter keys:", missingFront.join(", "));
  process.exit(2);
}

/* Headings */
const missingHeads = requiredHeads
  .filter((h) => !new RegExp(`^${h}`, "m").test(content))
  .filter(
    (h) => !(h.includes("Directory") && /## 🗂 Directory.*$/m.test(content))
  ); // allow either variant
if (missingHeads.length) {
  console.error("Missing headings:", missingHeads.join(", "));
  process.exit(3);
}

console.log("✅  Spec passed lint");
