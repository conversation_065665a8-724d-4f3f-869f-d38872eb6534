---
title: Milestone <ID> — <One-line scope>
description: <Short paragraph of intent>
created: <YYYY-MM-DD>
version: 0.0.0
status: Draft
tags: [milestone]
authors: []
---

import { Callout } from '@/components/Callout'

<Callout emoji="🚧">
<strong>Draft.</strong> Replace placeholders before PR.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
# pin exact versions or "inherit-from-root"
```

---

## 🎯 Definition of Done

<text>

---

## 📦 Deliverables

| Path | Must contain … |
|------|----------------|
|      |                |

---

## 🗂 Directory / API Diagram

```text
# Tree or sequence diagram as needed
```

---

## 🧠 Key Decisions

| Topic  | Decision | Rationale |
|--------|----------|-----------|
|        |          |           |

---

## ✅ Success Criteria

- SC-1
- SC-2

---

## 🔨 Task Breakdown

| #  | Branch name | Checklist item |
|----|-------------|---------------|
|    |             |               |

---

## 🤖 CI Pipeline (ready-to-copy)

```yaml
# Workflow snippet
```

---

## 🧪 Acceptance Tests

<text>

---

## 🔄 Document History

| Version | Date | Changes | Author | Milestone Status |
|---------|------|---------|--------|------------------|
| 0.1.0 | <YYYY-MM-DD> | Initial specification | <author> | Draft |

### Status Progression
- **Draft** → **Approved** → **In Progress** → **In Review** → **Completed**

### Update Guidelines
- Increment version for significant changes (new requirements, scope changes)
- Update milestone status when implementation phase changes
- Document all major decisions and scope modifications
- Link to related ADRs when architectural decisions are made

---

## 📚 Related Documentation

### Cross-References
- **ADRs**: List any architectural decisions that impact this milestone
- **Dependencies**: Reference other milestones this depends on
- **Domains**: Link to relevant domain specifications

### External Resources
- **Technical References**: Links to external documentation, APIs, libraries
- **Research**: Background research and analysis documents

<Callout emoji="📝">
Must pass <Link href="../spec-checklist.mdx">spec-checklist</Link> & dry-run before moving to <code>status: Approved</code>.
</Callout>

<Callout emoji="🔗">
Remember to update the <Link href="../milestone-log.mdx">milestone-log.mdx</Link> when this milestone's status or progress changes.
</Callout>