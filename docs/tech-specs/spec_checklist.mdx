---
title: Spec Checklist
description: Mandatory sections every milestone spec must include; used by spec-lint.
version: 1.0.0
status: Living
tags: [checklist]
---

## Required front-matter fields

- `title`
- `description`
- `created`
- `version`
- `status`
- `tags`

## Required top-level headings <sup>(exact text)</sup>

1. `## 🧳 Toolchain Versions`
2. `## 🎯 Definition of Done`
3. `## 📦 Deliverables`
4. `## 🗂 Directory` <em>or</em> `## 🗂 Directory / API Diagram`
5. `## 🧠 Key Decisions`
6. `## ✅ Success Criteria`
7. `## 🔨 Task Breakdown`
8. `## 🤖 CI Pipeline` <em>(or "CI Pipeline (ready-to-copy)")</em>
9. `## 🧪 Acceptance Tests`

## Approval gate

- PR must include output of:

  ```sh
  node scripts/spec-lint.mjs <file>
  ```
  – no missing sections, no TODO placeholders.
- After spec merges as **Approved**, run agent **dry-run** using:

  ```sh
  pnpm run agent:dry-run --spec <file>
  ```
  (implemented later)

---

import { Callout } from '@/components/Callout'

<Callout emoji="📋">
If any required section is intentionally empty, still include the heading and write "_N/A for this milestone_".
</Callout>
